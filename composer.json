{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "barryvdh/laravel-dompdf": "*", "diglactic/laravel-breadcrumbs": "^9.0", "guzzlehttp/guzzle": "^7.8", "http-interop/http-factory-guzzle": "^1.2", "laravel/framework": "^11.0", "laravel/sanctum": "^4.0", "laravel/scout": "^10.11", "laravel/tinker": "^2.9", "league/flysystem-aws-s3-v3": "^3.29", "meilisearch/meilisearch-php": "^1.11", "openpayu/openpayu": "^2.3", "spatie/laravel-medialibrary": "^11.7", "symfony/http-client": "*", "symfony/postmark-mailer": "*"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^10.5", "spatie/laravel-ignition": "^2.4"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}