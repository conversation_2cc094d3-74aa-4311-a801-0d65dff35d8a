<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add indexes for product recommendations
        Schema::table('products', function (Blueprint $table) {
            // Index for category-based recommendations
            $table->index(['category_id', 'stock', 'status'], 'products_category_stock_status_idx');
            
            // Index for stock and status filtering
            $table->index(['stock', 'status', 'created_at'], 'products_stock_status_created_idx');
            
            // Index for price-based sorting
            $table->index(['price', 'created_at'], 'products_price_created_idx');
        });

        // Add indexes for order-based recommendations
        Schema::table('orders', function (Blueprint $table) {
            // Index for user order history
            $table->index(['user_id', 'status', 'created_at'], 'orders_user_status_created_idx');
            
            // Index for completed orders
            $table->index(['status', 'created_at'], 'orders_status_created_idx');
        });

        // Add indexes for order items (frequently bought together)
        Schema::table('order_items', function (Blueprint $table) {
            // Index for product-based queries
            $table->index(['product_id', 'order_id'], 'order_items_product_order_idx');
            
            // Index for order-based queries
            $table->index(['order_id', 'product_id'], 'order_items_order_product_idx');
        });

        // Add indexes for cart operations
        Schema::table('carts', function (Blueprint $table) {
            // Index for user cart lookup
            $table->index(['user_id', 'status'], 'carts_user_status_idx');
            
            // Index for status filtering
            $table->index(['status', 'updated_at'], 'carts_status_updated_idx');
        });

        // Add indexes for cart items
        Schema::table('cart_items', function (Blueprint $table) {
            // Index for cart-based queries
            $table->index(['cart_id', 'product_id'], 'cart_items_cart_product_idx');
            
            // Index for product-based queries
            $table->index(['product_id', 'cart_id'], 'cart_items_product_cart_idx');
        });

        // Add indexes for categories
        Schema::table('categories', function (Blueprint $table) {
            // Index for visible categories
            $table->index(['is_visible', 'parent_id'], 'categories_visible_parent_idx');
            
            // Index for category hierarchy
            $table->index(['parent_id', 'sort'], 'categories_parent_sort_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex('products_category_stock_status_idx');
            $table->dropIndex('products_stock_status_created_idx');
            $table->dropIndex('products_price_created_idx');
        });

        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex('orders_user_status_created_idx');
            $table->dropIndex('orders_status_created_idx');
        });

        Schema::table('order_items', function (Blueprint $table) {
            $table->dropIndex('order_items_product_order_idx');
            $table->dropIndex('order_items_order_product_idx');
        });

        Schema::table('carts', function (Blueprint $table) {
            $table->dropIndex('carts_user_status_idx');
            $table->dropIndex('carts_status_updated_idx');
        });

        Schema::table('cart_items', function (Blueprint $table) {
            $table->dropIndex('cart_items_cart_product_idx');
            $table->dropIndex('cart_items_product_cart_idx');
        });

        Schema::table('categories', function (Blueprint $table) {
            $table->dropIndex('categories_visible_parent_idx');
            $table->dropIndex('categories_parent_sort_idx');
        });
    }
};
