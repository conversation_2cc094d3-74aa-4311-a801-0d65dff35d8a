<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\Order;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class InvoiceService
{
    /**
     * Generate invoice for an order
     */
    public function generateInvoice(Order $order): Invoice
    {
        // Check if invoice already exists
        $existingInvoice = $order->invoice;
        if ($existingInvoice) {
            return $existingInvoice;
        }

        // Generate invoice number
        $invoiceNumber = $this->generateInvoiceNumber();

        // Prepare invoice data
        $invoiceData = $this->prepareInvoiceData($order, $invoiceNumber);

        // Create invoice record
        $invoice = Invoice::create($invoiceData);

        return $invoice;
    }

    /**
     * Generate PDF for an invoice
     */
    public function generatePdf(Invoice $invoice): string
    {
        // Load the order relationship if not already loaded
        $invoice->load('order.items.product');

        // Generate PDF using DomPDF
        $pdf = Pdf::loadView('invoices.pdf', compact('invoice'))
            ->setPaper(config('invoice.pdf.paper', 'A4'), config('invoice.pdf.orientation', 'portrait'))
            ->setOptions([
                'dpi' => config('invoice.pdf.dpi', 96),
                'defaultFont' => config('invoice.pdf.font_family', 'DejaVu Sans'),
                'isRemoteEnabled' => true, // Allow loading external images (logo)
                'isHtml5ParserEnabled' => true,
                'isFontSubsettingEnabled' => true,
            ]);

        // Generate filename
        $filename = $this->generatePdfFilename($invoice);
        
        // Save PDF to storage
        $pdfContent = $pdf->output();
        $path = "invoices/{$filename}";
        Storage::disk('public')->put($path, $pdfContent);

        // Update invoice with PDF path
        $invoice->update([
            'pdf_path' => $path,
            'pdf_generated_at' => now(),
        ]);

        return $path;
    }

    /**
     * Generate unique invoice number
     */
    protected function generateInvoiceNumber(): string
    {
        $config = config('invoice.numbering');
        $prefix = $config['prefix'];
        $format = $config['format'];
        $padding = $config['padding'];
        $resetYearly = $config['reset_yearly'];
        $resetMonthly = $config['reset_monthly'];

        $now = Carbon::now();
        $year = $now->year;
        $month = $now->format('m');

        // Build the base query
        $query = Invoice::query();
        
        if ($resetYearly) {
            $query->whereYear('created_at', $year);
        }
        
        if ($resetMonthly) {
            $query->whereMonth('created_at', $now->month);
        }

        // Get the next sequential number
        $lastInvoice = $query->orderBy('id', 'desc')->first();
        $nextNumber = 1;

        if ($lastInvoice) {
            // Extract number from last invoice
            $pattern = '/(\d+)$/';
            if (preg_match($pattern, $lastInvoice->invoice_number, $matches)) {
                $nextNumber = intval($matches[1]) + 1;
            }
        }

        // Format the number with padding
        $formattedNumber = str_pad($nextNumber, $padding, '0', STR_PAD_LEFT);

        // Replace placeholders in format
        $invoiceNumber = str_replace([
            '{prefix}',
            '{year}',
            '{month}',
            '{number}'
        ], [
            $prefix,
            $year,
            $month,
            $formattedNumber
        ], $format);

        // Ensure uniqueness
        while (Invoice::where('invoice_number', $invoiceNumber)->exists()) {
            $nextNumber++;
            $formattedNumber = str_pad($nextNumber, $padding, '0', STR_PAD_LEFT);
            $invoiceNumber = str_replace([
                '{prefix}',
                '{year}',
                '{month}',
                '{number}'
            ], [
                $prefix,
                $year,
                $month,
                $formattedNumber
            ], $format);
        }

        return $invoiceNumber;
    }

    /**
     * Prepare invoice data from order
     */
    protected function prepareInvoiceData(Order $order, string $invoiceNumber): array
    {
        $now = Carbon::now();
        $paymentTermsDays = config('invoice.legal.payment_terms', '14 dni');
        
        // Extract number of days from payment terms
        $daysMatch = [];
        if (preg_match('/(\d+)/', $paymentTermsDays, $daysMatch)) {
            $paymentDays = intval($daysMatch[1]);
        } else {
            $paymentDays = 14; // Default fallback
        }

        return [
            'invoice_number' => $invoiceNumber,
            'order_id' => $order->id,
            'issue_date' => $now->toDateString(),
            'sale_date' => $now->toDateString(), // Same as issue date for immediate sales
            'due_date' => $now->addDays($paymentDays)->toDateString(),
            'seller_data' => $this->getSellerData(),
            'buyer_data' => $this->getBuyerData($order),
            'subtotal' => $this->calculateSubtotal($order),
            'vat_amount' => $this->calculateVatAmount($order),
            'total' => $order->total,
            'currency' => config('invoice.settings.currency', 'PLN'),
            'items' => $this->prepareInvoiceItems($order),
            'payment_method' => $this->formatPaymentMethod($order->payment_method),
            'payment_terms_days' => $paymentDays,
            'status' => 'draft',
        ];
    }

    /**
     * Get seller data from configuration
     */
    protected function getSellerData(): array
    {
        return config('invoice.seller');
    }

    /**
     * Get buyer data from order
     */
    protected function getBuyerData(Order $order): array
    {
        $buyerName = trim($order->first_name . ' ' . $order->last_name);
        
        // Use billing address if available, otherwise use delivery address
        $address = $order->billing_address ?? $order->delivery_address;
        
        if (is_array($address)) {
            return [
                'name' => $buyerName,
                'email' => $order->email,
                'phone' => $order->phone,
                'address' => [
                    'street' => $address['street'] ?? '',
                    'building_number' => $address['building_number'] ?? '',
                    'apartment_number' => $address['apartment_number'] ?? '',
                    'post_code' => $address['post_code'] ?? '',
                    'city' => $address['city'] ?? '',
                    'country' => $address['country'] ?? 'Polska',
                ],
            ];
        }

        // Fallback if no proper address data
        return [
            'name' => $buyerName,
            'email' => $order->email,
            'phone' => $order->phone,
            'address' => [
                'street' => '',
                'building_number' => '',
                'apartment_number' => '',
                'post_code' => '',
                'city' => '',
                'country' => 'Polska',
            ],
        ];
    }

    /**
     * Calculate subtotal (net amount)
     */
    protected function calculateSubtotal(Order $order): float
    {
        // For now, we'll calculate based on total minus VAT
        // In a real implementation, you might want to store net prices separately
        $vatRate = config('invoice.vat.default_rate', 23) / 100;
        $subtotal = $order->total / (1 + $vatRate);
        
        return round($subtotal, 2);
    }

    /**
     * Calculate VAT amount
     */
    protected function calculateVatAmount(Order $order): float
    {
        $subtotal = $this->calculateSubtotal($order);
        return round($order->total - $subtotal, 2);
    }

    /**
     * Prepare invoice items from order items
     */
    protected function prepareInvoiceItems(Order $order): array
    {
        $items = [];
        $vatRate = config('invoice.vat.default_rate', 23);
        
        foreach ($order->items as $orderItem) {
            $priceGross = $orderItem->price;
            $priceNet = $priceGross / (1 + ($vatRate / 100));
            $vatAmount = $priceGross - $priceNet;
            
            $items[] = [
                'name' => $orderItem->name,
                'quantity' => $orderItem->quantity,
                'price_net' => round($priceNet, 2),
                'price_gross' => $priceGross,
                'vat_rate' => $vatRate,
                'vat_amount' => round($vatAmount * $orderItem->quantity, 2),
                'total_net' => round($priceNet * $orderItem->quantity, 2),
                'total_gross' => round($priceGross * $orderItem->quantity, 2),
            ];
        }
        
        return $items;
    }

    /**
     * Format payment method for display
     */
    protected function formatPaymentMethod(string $paymentMethod): string
    {
        $methods = [
            'payu' => 'PayU (płatność online)',
            'bank_transfer' => 'Przelew bankowy',
            'cash' => 'Gotówka',
            'card' => 'Karta płatnicza',
        ];

        return $methods[$paymentMethod] ?? 'Przelew bankowy';
    }

    /**
     * Generate PDF filename
     */
    protected function generatePdfFilename(Invoice $invoice): string
    {
        $cleanNumber = Str::slug($invoice->invoice_number, '_');
        return "faktura_{$cleanNumber}.pdf";
    }

    /**
     * Get invoice PDF download response
     */
    public function downloadPdf(Invoice $invoice)
    {
        if (!$invoice->hasPdf()) {
            $this->generatePdf($invoice);
        }

        $path = storage_path('app/public/' . $invoice->pdf_path);
        
        if (!file_exists($path)) {
            throw new \Exception('PDF file not found');
        }

        return response()->download($path, $invoice->pdf_file_name);
    }
}
