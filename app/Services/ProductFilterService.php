<?php

namespace App\Services;

use App\Models\Product;

class ProductFilterService
{
    public function filterAndSort($categoryIds, $filters)
    {
        // Start building the product query
        $query = Product::inStock()->whereIn('category_id', $categoryIds);

        // Apply filter by multiple producents if provided
        if (!empty($filters['producent_id'])) {
            // Check if it's an array of producent IDs
            if (is_array($filters['producent_id'])) {
                $query->whereIn('producent_id', $filters['producent_id']);
            } else {
                $query->where('producent_id', $filters['producent_id']);
            }
        }

        // Apply filter by properties if provided
        if (!empty($filters['properties'])) {
            $properties = $filters['properties'];
            $query->whereHas('productProperties', function ($q) use ($properties) {
                foreach ($properties as $propertyId => $optionId) {
                    $q->where('property_id', $propertyId)
                      ->where('property_option_id', $optionId);
                }
            });
        }

        // Apply sorting if provided
        if (!empty($filters['sort'])) {
            $sort = $filters['sort'];
            switch ($sort) {
                case 'name-asc':
                    $query->orderBy('name', 'asc');
                    break;
                case 'name-desc':
                    $query->orderBy('name', 'desc');
                    break;
                case 'price-asc':
                    $query->orderBy('price', 'asc');
                    break;
                case 'price-desc':
                    $query->orderBy('price', 'desc');
                    break;
                default:
                    $query->orderBy('name', 'asc'); // Default sort
                    break;
            }
        }else{
            $query->orderBy('name', 'asc'); // Default sort
        }

        return $query->paginate(24)->appends(request()->query());
    }
}
