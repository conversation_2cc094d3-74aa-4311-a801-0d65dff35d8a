<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class FullImportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct()
    {
        //
    }

    public function handle()
    {
        
        // ImportProductsJob(1)->dispatch(); // Start with page 1 for products
        // Chain jobs: Import Categories twice, then Import Producents, then Import Products page by page
        ImportCategoriesJob::withChain([
            new ImportCategoriesJob(),
            new ImportProducentsJob(),
            new ImportProductsJob(1) // Start with page 1 for products
        ])->dispatch();
    }
}
