<?php

namespace App\Models;

use App\Traits\HasFiltersAndSorts;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    use HasFactory, HasFiltersAndSorts;

    protected $fillable = [
        'name',
        'description',
        'molos_id',
        'slug',
        'path',
        'sort',
        'price_percentage',
        'parent_id',
        'is_visible'
    ];

    protected static $filters = [
        'name' => ['type' => 'text'],
    ];

    protected static $sorts = [
        'name',
        'description',
        'priority'
    ];

    public function products()
    {
        return $this->hasMany(Product::class);
    }

    public function children()
    {
        return $this->hasMany(Category::class, 'parent_id');
    }

    public function parent()
    {
        return $this->belongsTo(Category::class, 'parent_id');
    }

    // Define the relationship with properties
    public function properties()
    {
        return $this->belongsToMany(Property::class, 'category_property');
    }

    public static function getCategoriesHierarchically($parentId = null, $level = 0)
    {
        $categories = self::where('parent_id', $parentId)->orderBy('name')->get();
        $result = [];

        foreach ($categories as $category) {
            $category->name = str_repeat('--', $level) . ' ' . $category->name;
            $result[] = $category;
            $result = array_merge($result, self::getCategoriesHierarchically($category->id, $level + 1));
        }

        return $result;
    }

    public function getAncestorIds()
    {
        $ancestors = [];
        $currentCategory = $this;

        while ($currentCategory->parent) {
            $currentCategory = $currentCategory->parent;
            $ancestors[] = $currentCategory->id;
        }

        return $ancestors;
    }

    /**
     * Get the full category path as a string
     *
     * @param string $separator
     * @return string
     */
    public function getFullPath(string $separator = ' > '): string
    {
        $path = [$this->name];
        $current = $this;

        while ($current->parent) {
            $current = $current->parent;
            array_unshift($path, $current->name);
        }

        return implode($separator, $path);
    }

    /**
     * Get all descendant category IDs
     *
     * @return array
     */
    public function getDescendantIds(): array
    {
        $descendants = [];

        foreach ($this->children as $child) {
            $descendants[] = $child->id;
            $descendants = array_merge($descendants, $child->getDescendantIds());
        }

        return $descendants;
    }

    public function getUrlAttribute()
    {
        return $this->path;
    }

    public function scopeMenu($query)
    {
        return $query->where('is_visible', true)
            ->whereNull('parent_id')
            ->orderBy('sort', 'asc');
    }

    public function scopeVisible($query)
    {
        return $query->where('is_visible', true);
    }

    public function scopeAdmin($query)
    {
        return $query->orderBy('sort', 'asc');
    }
}
