<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Invoice extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_number',
        'order_id',
        'issue_date',
        'due_date',
        'sale_date',
        'seller_data',
        'buyer_data',
        'subtotal',
        'vat_amount',
        'total',
        'currency',
        'items',
        'notes',
        'payment_method',
        'payment_terms_days',
        'pdf_path',
        'pdf_generated_at',
        'status',
    ];

    protected $casts = [
        'issue_date' => 'date',
        'due_date' => 'date',
        'sale_date' => 'date',
        'seller_data' => 'array',
        'buyer_data' => 'array',
        'items' => 'array',
        'subtotal' => 'decimal:2',
        'vat_amount' => 'decimal:2',
        'total' => 'decimal:2',
        'pdf_generated_at' => 'datetime',
    ];

    /**
     * Get the order that this invoice belongs to
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Check if the invoice is overdue
     */
    public function isOverdue(): bool
    {
        return $this->due_date < Carbon::now()->toDateString() && $this->status !== 'paid';
    }

    /**
     * Get formatted invoice number for display
     */
    public function getFormattedNumberAttribute(): string
    {
        return $this->invoice_number;
    }

    /**
     * Get the PDF file name
     */
    public function getPdfFileNameAttribute(): string
    {
        return "faktura_{$this->invoice_number}.pdf";
    }

    /**
     * Check if PDF has been generated
     */
    public function hasPdf(): bool
    {
        return !is_null($this->pdf_path) && !is_null($this->pdf_generated_at);
    }
}
