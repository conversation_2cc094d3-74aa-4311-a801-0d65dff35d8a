<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Property extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'type', 'is_multiple'];

    // Define the relationship with categories
    public function categories()
    {
        return $this->belongsToMany(Category::class, 'category_property');
    }

    public function options()
    {
        return $this->hasMany(PropertyOption::class);
    }
}
