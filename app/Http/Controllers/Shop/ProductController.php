<?php

namespace App\Http\Controllers\Shop;

use App\Http\Controllers\Controller;
use App\Models\Product;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    /**
     * Display the product detail page.
     *
     * @param Request $request
     * @param string $path
     * @param Product $product
     * @return \Illuminate\View\View
     */
    public function detail(Request $request, $path, Product $product)
    {
        // Optionally, you can verify that the product belongs to the category represented by $path.
        // This ensures that the URL is valid and the product is in the correct category.

        // Example: Check if the product's category path matches the $path parameter.
        // Assuming you have a method in your Product model to get the full category path.
        // dd($product->category->full_path);
        // if ($product->category->full_path !== $path) {
        //     abort(404);
        // }

        // Load any relationships needed for the view.
        // For example, load product images, attributes, etc.
        // $product->load(['images', 'attributes']);

        // Pass the product to the view.
        return view('app.pages.detail', compact('product'));
    }
}
