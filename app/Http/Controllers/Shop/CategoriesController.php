<?php

namespace App\Http\Controllers\Shop;

use App\Models\Category;
use App\Models\Product;
use App\Models\Producent;
use App\Services\ProductFilterService;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class CategoriesController extends Controller
{

    protected $productFilterService;

    public function __construct(ProductFilterService $productFilterService)
    {
        $this->productFilterService = $productFilterService;
    }

    public function showCategory($path, Request $request)
    {
        // Find the category based on the path and check visibility
        $category = Category::where('path', $path)
            ->visible()
            ->firstOrFail();
    
        $categoryIds = $this->getDescendantCategoryIds($category);
        $categoryIds[] = $category->id; // Include the current category
    
        // Gather filters from the request, including sorting from the form
        $filters = [
            'producent_id' => $request->input('filter.producent_id', []),  // Accept multiple producent IDs
            'properties'   => $request->input('filter.properties'),
            'sort'         => $request->input('orderby') // Use the orderby field from the form
        ];
    
        // Use the service to filter and sort products
        $products = $this->productFilterService->filterAndSort($categoryIds, $filters);
    
        // Fetch children categories
        $children = $category->children;
    
        // Pass the current sort option to the view
        $orderby = $filters['sort'] ?? 'name-asc'; // Default to name ascending if not provided

        $producents = Producent::whereHas('products', function ($query) use ($categoryIds) {
            $query->whereIn('category_id', $categoryIds);
        })->get();
        $filter = [
            // Existing filters can be added here
            [
                'type' => 'checkbox', // Indicates the type of filter
                'name' => 'Producent', // Display name for the filter
                'code' => 'producent_id', // Key used in filters and form
                'variables' => $producents, // List of producents
            ],
            // Add other filter items as needed
            // Example:
            // [
            //     'type' => 'period',
            //     'name' => 'Price',
            //     'code' => 'price',
            //     'min' => 0,
            //     'max' => 1000,
            //     'from' => $request->input('filter.price.from', 0),
            //     'to' => $request->input('filter.price.to', 1000),
            // ],
        ];
    
        return view('app.pages.category', compact('category', 'products', 'children', 'orderby', 'filter'));
    }

    public function index()
    {
        $categories = Category::whereNull('parent_id')->get();
        return view('app.categories.index', compact('categories'));
    }

    private function getDescendantCategoryIds($category)
    {
        $descendants = collect();

        foreach ($category->children()->visible()->get() as $child) {
            $descendants->push($child->id);
            $descendants = $descendants->merge($this->getDescendantCategoryIds($child));
        }

        return $descendants->all();
    }

    public function ajaxSearch(Request $request)
    {
        $query = $request->input('search');

        $products = Product::search($query)
            ->query(function ($builder) use ($request) {
                // Apply additional filters if necessary
                if ($request->filled('category')) {
                    $builder->where('category', $request->input('category'));
                }
                // Add more filters as needed
            })
            ->take(5)
            ->get()
            ->map(function ($product) {
                return [
                    'label' => $product->name,
                    'value' => route('detail', ['path' => $product->category->url, 'product' => $product]),
                ];
            });

        return response()->json($products);
    }
}
