<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Property;
use App\Models\PropertyOption;
use Illuminate\Http\Request;

class PropertyController extends Controller
{
    public function index()
    {
        $properties = Property::with('options', 'categories')->get();
        return view('admin.properties.index', compact('properties'));
    }

    public function create()
    {
        $categories = Category::getCategoriesHierarchically();
        return view('admin.properties.create', compact('categories'));
    }

    public function edit(Property $property)
    {
        $categories = Category::getCategoriesHierarchically();
        return view('admin.properties.edit', compact('property', 'categories'));
    }
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:text,select',
            'is_multiple' => 'boolean',
            'options' => 'array|nullable',
            'options.*' => 'nullable|string|max:255'
        ]);

        $property = Property::create([
            'name' => $request->name,
            'type' => $request->type,
            'is_multiple' => $request->is_multiple ? true : false,
        ]);

        if ($request->has('options')) {
            foreach ($request->options as $option) {
                if (!is_null($option) && $option !== '') {
                    $property->options()->create(['value' => $option]);
                }
            }
        }

        if ($request->input('action') === 'save') {
            return redirect()->route('admin.properties.index')->with('success', 'Property created successfully.');
        }

        return redirect()->route('admin.properties.edit', $property->id)->with('success', 'Property created successfully.');
    }

    public function update(Request $request, Property $property)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:text,select',
            'is_multiple' => 'boolean',
            'options' => 'array|nullable',
            'options.*' => 'nullable|string|max:255'
        ]);

        $property->update([
            'name' => $request->name,
            'type' => $request->type,
            'is_multiple' => $request->is_multiple ? true : false,
        ]);

        if ($request->has('options')) {
            foreach ($request->options as $index => $option) {
                if ($option) {
                    if (isset($property->options[$index])) {
                        $property->options[$index]->update(['value' => $option]);
                    } else {
                        $property->options()->create(['value' => $option]);
                    }
                }
            }
        }

        if ($request->input('action') === 'save') {
            return redirect()->route('admin.properties.index')->with('success', 'Property updated successfully.');
        }

        return redirect()->route('admin.properties.edit', $property->id)->with('success', 'Property updated successfully.');
    }

    public function destroy(Property $property)
    {
        $property->delete();
        return redirect()->route('admin.properties.index')->with('success', 'Property deleted successfully.');
    }
}
